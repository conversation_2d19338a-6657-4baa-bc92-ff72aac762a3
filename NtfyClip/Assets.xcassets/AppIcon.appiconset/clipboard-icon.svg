<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <!-- Background Circle -->
    <circle cx="512" cy="512" r="512" fill="#E6F0FF"/>

    <!-- Icon with <PERSON>rad<PERSON> -->
    <defs>
        <linearGradient id="iconGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#007AFF;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#00AFFF;stop-opacity:1" />
        </linearGradient>
    </defs>

    <!-- SF Symbol: doc.on.clipboard - This is an approximation centered and scaled -->
    <g transform="translate(232, 232) scale(1.5)">
        <path fill="url(#iconGradient)" d="M128 32H112V16C112 7.163 104.837 0 96 0H64C55.163 0 48 7.163 48 16V32H32C14.327 32 0 46.327 0 64V288C0 305.673 14.327 320 32 320H192C209.673 320 224 305.673 224 288V64C224 46.327 209.673 32 192 32ZM64 16H96V48H64V16ZM192 288H32V64H48V80C48 88.837 55.163 96 64 96H160C168.837 96 176 88.837 176 80V64H192V288Z"/>
        <path fill="url(#iconGradient)" d="M288 96H272V80C272 71.163 264.837 64 256 64H224C215.163 64 208 71.163 208 80V96H192C174.327 96 160 110.327 160 128V352C160 369.673 174.327 384 192 384H352C369.673 384 384 369.673 384 352V128C384 110.327 369.673 96 352 96ZM224 80H256V112H224V80ZM352 352H192V128H208V144C208 152.837 215.163 160 224 160H320C328.837 160 336 152.837 336 144V128H352V352Z"/>
    </g>
</svg>
