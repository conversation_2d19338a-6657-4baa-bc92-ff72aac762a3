import SwiftUI

struct StatusDetailView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @StateObject private var configManager = ConfigurationManager()
    @StateObject private var logManager = LogManager.shared
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 32) {
                // Header
                headerSection

                // Statistics - moved up to replace dashboard
                statisticsSection

                // Connection Status
                connectionStatusSection

                // Configuration Status
                configurationStatusSection

                // Recent Activity
                recentActivitySection

                Spacer(minLength: 32)
            }
            .padding(.horizontal, 32)
            .padding(.vertical, 24)
        }
        .navigationTitle("Status")
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                HStack(spacing: 16) {
                    Image(systemName: "doc.on.clipboard")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundStyle(.blue.gradient)
                        .frame(width: 50, height: 50)
                        .background(
                            Circle()
                                .fill(.blue.opacity(0.1))
                        )

                    VStack(alignment: .leading, spacing: 6) {
                        Text("NtfyClip")
                            .font(.largeTitle)
                            .fontWeight(.bold)

                        Text("Clipboard Sync for macOS")
                            .font(.title3)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Overall Status Indicator
                VStack(alignment: .trailing, spacing: 8) {
                    HStack(spacing: 8) {
                        Circle()
                            .fill(overallStatusColor)
                            .frame(width: 12, height: 12)
                            .overlay(
                                Circle()
                                    .stroke(overallStatusColor.opacity(0.3), lineWidth: 2)
                                    .scaleEffect(appViewModel.isConnected ? 1.8 : 1.0)
                                    .opacity(appViewModel.isConnected ? 0 : 1)
                                    .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: false),
                                              value: appViewModel.isConnected)
                            )

                        Text(overallStatusText)
                            .font(.headline)
                            .foregroundColor(overallStatusColor)
                            .fontWeight(.semibold)
                    }

                    if let lastSyncTime = appViewModel.lastSyncTime {
                        Text("Last sync: \(lastSyncTime, style: .relative)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Divider()
        }
        .padding(.bottom, 8)
    }



    // MARK: - Connection Status Section
    private var connectionStatusSection: some View {
        ModernGroupBox(title: "Connection Status", icon: "wifi") {
            VStack(spacing: 16) {
                StatusRow(
                    icon: "arrow.triangle.2.circlepath",
                    title: "Sync Status",
                    value: appViewModel.isSyncEnabled ? "Enabled" : "Disabled",
                    color: appViewModel.isSyncEnabled ? .green : .secondary
                )

                StatusRow(
                    icon: "arrow.up.circle",
                    title: "Send Service",
                    value: configManager.enableSending ? (configManager.isSendConfigurationValid() ? "Ready" : "Invalid Config") : "Disabled",
                    color: sendStatusColor
                )

                StatusRow(
                    icon: "arrow.down.circle",
                    title: "Receive Service",
                    value: configManager.enableReceiving ? (configManager.isReceiveConfigurationValid() ? "Ready" : "Invalid Config") : "Disabled",
                    color: receiveStatusColor
                )
            }
        }
    }
    
    // MARK: - Configuration Status Section
    private var configurationStatusSection: some View {
        ModernGroupBox(title: "Configuration", icon: "gearshape") {
            VStack(spacing: 20) {
                // Send Configuration
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "paperplane")
                            .foregroundColor(.blue)
                            .frame(width: 20)

                        Text("Send Configuration")
                            .font(.headline)
                            .fontWeight(.medium)

                        Spacer()

                        Image(systemName: configManager.isSendConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(configManager.isSendConfigurationValid() ? .green : .red)
                            .font(.title3)
                    }

                    if configManager.enableSending && configManager.isSendConfigurationValid() {
                        VStack(alignment: .leading, spacing: 6) {
                            ConfigDetailRow(label: "URL", value: configManager.sendServerURL)
                            ConfigDetailRow(label: "Topic", value: configManager.sendTopicName)
                        }
                        .padding(.leading, 28)
                    }
                }

                Divider()

                // Receive Configuration
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "tray.and.arrow.down")
                            .foregroundColor(.green)
                            .frame(width: 20)

                        Text("Receive Configuration")
                            .font(.headline)
                            .fontWeight(.medium)

                        Spacer()

                        Image(systemName: configManager.isReceiveConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(configManager.isReceiveConfigurationValid() ? .green : .red)
                            .font(.title3)
                    }

                    if configManager.enableReceiving && configManager.isReceiveConfigurationValid() {
                        VStack(alignment: .leading, spacing: 6) {
                            ConfigDetailRow(label: "URL", value: configManager.receiveServerURL)
                            ConfigDetailRow(label: "Topic", value: configManager.receiveTopicName)
                        }
                        .padding(.leading, 28)
                    }
                }
            }
        }
    }
    
    // MARK: - Statistics Section
    private var statisticsSection: some View {
        ModernGroupBox(title: "Statistics", icon: "chart.bar") {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 20) {
                ModernStatisticCard(
                    title: "Items Sent",
                    value: "\(appViewModel.itemsSentCount)",
                    icon: "arrow.up.circle.fill",
                    color: .blue
                )

                ModernStatisticCard(
                    title: "Items Received",
                    value: "\(appViewModel.itemsReceivedCount)",
                    icon: "arrow.down.circle.fill",
                    color: .green
                )

                ModernStatisticCard(
                    title: "Total Synced",
                    value: "\(appViewModel.clipboardItemsCount)",
                    icon: "doc.on.clipboard",
                    color: .purple
                )

                ModernStatisticCard(
                    title: "Uptime",
                    value: formatUptime(),
                    icon: "clock",
                    color: .orange
                )
            }
        }
    }
    
    // MARK: - Recent Activity Section
    private var recentActivitySection: some View {
        ModernGroupBox(title: "Recent Activity", icon: "clock.arrow.circlepath") {
            VStack(alignment: .leading, spacing: 12) {
                if logManager.recentLogs.isEmpty {
                    VStack(spacing: 12) {
                        Image(systemName: "clock.badge.questionmark")
                            .font(.system(size: 32))
                            .foregroundColor(.secondary)

                        Text("No recent activity")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Text("Activity will appear here once sync starts")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 20)
                } else {
                    ForEach(logManager.recentLogs.prefix(5), id: \.id) { log in
                        ActivityRow(log: log)

                        if log.id != logManager.recentLogs.prefix(5).last?.id {
                            Divider()
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Properties
    private var overallStatusColor: Color {
        if appViewModel.errorMessage != nil {
            return .red
        } else if appViewModel.isConnected {
            return .green
        } else if appViewModel.isSyncEnabled {
            return .orange
        } else {
            return .gray
        }
    }
    
    private var overallStatusText: String {
        if appViewModel.errorMessage != nil {
            return "Error"
        } else if appViewModel.isConnected {
            return "Connected"
        } else if appViewModel.isSyncEnabled {
            return "Connecting"
        } else {
            return "Disabled"
        }
    }
    
    private var sendStatusColor: Color {
        if !configManager.enableSending {
            return .secondary
        } else if configManager.isSendConfigurationValid() {
            return .green
        } else {
            return .red
        }
    }
    
    private var receiveStatusColor: Color {
        if !configManager.enableReceiving {
            return .secondary
        } else if configManager.isReceiveConfigurationValid() {
            return .green
        } else {
            return .red
        }
    }
    
    private func formatUptime() -> String {
        // This would be calculated from app start time
        return "Running"
    }
}

// MARK: - Modern Group Box with Liquid Glass
struct ModernGroupBox<Content: View>: View {
    let title: String
    let icon: String
    let content: Content

    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.accentColor)
                    .font(.title3)
                    .frame(width: 24, height: 24)
                    .background(
                        Circle()
                            .fill(.ultraThinMaterial)
                            .overlay(
                                Circle()
                                    .stroke(Color.accentColor.opacity(0.3), lineWidth: 1)
                            )
                    )

                Text(title)
                    .font(.title2)
                    .fontWeight(.semibold)

                Spacer()
            }

            content
        }
        .padding(20)
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                )
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Status Row
struct StatusRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)

            Text(title)
                .font(.body)

            Spacer()

            Text(value)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

// MARK: - Config Detail Row
struct ConfigDetailRow: View {
    let label: String
    let value: String

    var body: some View {
        HStack {
            Text("\(label):")
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 40, alignment: .leading)

            Text(value)
                .font(.caption)
                .foregroundColor(.primary)
                .lineLimit(1)
                .truncationMode(.middle)

            Spacer()
        }
    }
}

// MARK: - Modern Statistic Card
struct ModernStatisticCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .medium))
                .foregroundStyle(color.gradient)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(color.opacity(0.1))
                )

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                )
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Activity Row
struct ActivityRow: View {
    let log: LogEntry

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: log.level.icon)
                .foregroundColor(log.level.color)
                .font(.system(size: 14, weight: .medium))
                .frame(width: 20)

            VStack(alignment: .leading, spacing: 4) {
                Text(log.message)
                    .font(.body)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                HStack {
                    Text(log.category)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(Color.secondary.opacity(0.2))
                        )

                    Text(log.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Liquid Glass Components are defined in MainWindowView.swift
