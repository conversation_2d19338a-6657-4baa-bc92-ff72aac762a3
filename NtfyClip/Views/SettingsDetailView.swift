import SwiftUI

struct SettingsDetailView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @EnvironmentObject private var configManager: ConfigurationManager
    @State private var showingResetAlert = false
    @State private var selectedTab: SettingsTab = .general
    
    var body: some View {
        VStack(spacing: 0) {
            // Tab Bar with proper spacing
            VStack(spacing: 0) {
                Spacer()
                    .frame(height: 16) // Add top spacing

                modernTabBar
            }

            // Content
            ScrollView {
                VStack(alignment: .leading, spacing: 32) {
                    switch selectedTab {
                    case .general:
                        generalSettings
                    case .send:
                        sendSettings
                    case .receive:
                        receiveSettings
                    case .advanced:
                        advancedSettings
                    }
                }
                .padding(.horizontal, 32)
                .padding(.vertical, 24)
            }

            // Footer
            modernFooterSection
        }
        .navigationTitle("Settings")
    }
    
    // MARK: - Modern Tab Bar
    private var modernTabBar: some View {
        HStack(spacing: 4) {
            ForEach(SettingsTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selectedTab = tab
                    }
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: tab.icon)
                            .font(.system(size: 14, weight: .medium))

                        Text(tab.title)
                            .font(.system(size: 13, weight: .medium))
                    }
                    .foregroundColor(selectedTab == tab ? .white : .primary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(selectedTab == tab ? Color.accentColor : Color.clear)
                    )
                }
                .buttonStyle(.plain)
            }

            Spacer()
        }
        .padding(.horizontal, 32)
        .padding(.vertical, 20)
        .background(Color(NSColor.controlBackgroundColor))
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color(NSColor.separatorColor)),
            alignment: .bottom
        )
    }
    
    // MARK: - General Settings
    private var generalSettings: some View {
        VStack(alignment: .leading, spacing: 24) {
            SettingsGroupBox(title: "Sync Options", icon: "arrow.triangle.2.circlepath") {
                VStack(alignment: .leading, spacing: 16) {
                    SettingsToggle(
                        title: "Enable Sending",
                        subtitle: "Allow sending clipboard content to ntfy",
                        isOn: $configManager.enableSending
                    )

                    SettingsToggle(
                        title: "Enable Receiving",
                        subtitle: "Allow receiving content from ntfy to clipboard",
                        isOn: $configManager.enableReceiving
                    )

                    Divider()

                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Overall Status")
                                .font(.headline)
                                .fontWeight(.medium)

                            Text("Configuration validation result")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        HStack(spacing: 8) {
                            Image(systemName: configManager.isConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(configManager.isConfigurationValid() ? .green : .red)
                                .font(.title3)

                            Text(configManager.isConfigurationValid() ? "Valid" : "Invalid")
                                .font(.body)
                                .fontWeight(.medium)
                                .foregroundColor(configManager.isConfigurationValid() ? .green : .red)
                        }
                    }
                }
            }

            SettingsGroupBox(title: "Quick Actions", icon: "bolt") {
                VStack(spacing: 16) {
                    Button(action: {
                        appViewModel.toggleSync()
                    }) {
                        HStack {
                            Image(systemName: appViewModel.isSyncEnabled ? "stop.circle.fill" : "play.circle.fill")
                                .font(.title3)
                            Text(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
                                .fontWeight(.medium)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    .disabled(!configManager.isConfigurationValid())

                    Button("Test Configuration") {
                        testConfiguration()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.large)
                    .disabled(!configManager.isConfigurationValid())
                }
            }
        }
    }
    
    // MARK: - Send Settings
    private var sendSettings: some View {
        VStack(alignment: .leading, spacing: 24) {
            SettingsGroupBox(title: "Send Configuration", icon: "paperplane") {
                VStack(alignment: .leading, spacing: 20) {
                    SettingsTextField(
                        title: "Ntfy Server URL",
                        subtitle: "The URL of your ntfy server for sending messages",
                        placeholder: "https://ntfy.sh",
                        text: $configManager.sendServerURL
                    )

                    SettingsTextField(
                        title: "Topic Name",
                        subtitle: "Topic name for sending clipboard content",
                        placeholder: "my-clipboard-send",
                        text: $configManager.sendTopicName
                    )

                    // Configuration validation status
                    HStack {
                        Image(systemName: configManager.isSendConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(configManager.isSendConfigurationValid() ? .green : .red)
                            .font(.title3)

                        VStack(alignment: .leading, spacing: 2) {
                            Text(configManager.isSendConfigurationValid() ? "Configuration Valid" : "Configuration Invalid")
                                .font(.body)
                                .fontWeight(.medium)
                                .foregroundColor(configManager.isSendConfigurationValid() ? .green : .red)

                            Text(configManager.isSendConfigurationValid() ? "Ready to send messages" : "Please complete the configuration")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()
                    }
                    .padding(.top, 8)
                }
            }

            SettingsGroupBox(title: "Send Authentication", icon: "key") {
                VStack(alignment: .leading, spacing: 16) {
                    SettingsToggle(
                        title: "Use Authentication for Sending",
                        subtitle: "Enable if your ntfy server requires authentication",
                        isOn: $configManager.useSendAuthentication
                    )

                    if configManager.useSendAuthentication {
                        VStack(alignment: .leading, spacing: 16) {
                            SettingsTextField(
                                title: "Username",
                                subtitle: "Your ntfy server username",
                                placeholder: "Username",
                                text: $configManager.sendUsername
                            )

                            SettingsSecureField(
                                title: "Password",
                                subtitle: "Your ntfy server password",
                                placeholder: "Password",
                                text: $configManager.sendPassword
                            )
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                }
                .animation(.easeInOut(duration: 0.3), value: configManager.useSendAuthentication)
            }
        }
    }
    
    // MARK: - Receive Settings
    private var receiveSettings: some View {
        VStack(alignment: .leading, spacing: 24) {
            SettingsGroupBox(title: "Receive Configuration", icon: "tray.and.arrow.down") {
                VStack(alignment: .leading, spacing: 20) {
                    SettingsTextField(
                        title: "Ntfy Server URL",
                        subtitle: "The URL of your ntfy server for receiving messages",
                        placeholder: "https://ntfy.sh",
                        text: $configManager.receiveServerURL
                    )

                    SettingsTextField(
                        title: "Topic Name",
                        subtitle: "Topic name for receiving clipboard content",
                        placeholder: "my-clipboard-receive",
                        text: $configManager.receiveTopicName
                    )

                    // Configuration validation status
                    HStack {
                        Image(systemName: configManager.isReceiveConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(configManager.isReceiveConfigurationValid() ? .green : .red)
                            .font(.title3)

                        VStack(alignment: .leading, spacing: 2) {
                            Text(configManager.isReceiveConfigurationValid() ? "Configuration Valid" : "Configuration Invalid")
                                .font(.body)
                                .fontWeight(.medium)
                                .foregroundColor(configManager.isReceiveConfigurationValid() ? .green : .red)

                            Text(configManager.isReceiveConfigurationValid() ? "Ready to receive messages" : "Please complete the configuration")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()
                    }
                    .padding(.top, 8)
                }
            }

            SettingsGroupBox(title: "Receive Authentication", icon: "key") {
                VStack(alignment: .leading, spacing: 16) {
                    SettingsToggle(
                        title: "Use Authentication for Receiving",
                        subtitle: "Enable if your ntfy server requires authentication",
                        isOn: $configManager.useReceiveAuthentication
                    )

                    if configManager.useReceiveAuthentication {
                        VStack(alignment: .leading, spacing: 16) {
                            SettingsTextField(
                                title: "Username",
                                subtitle: "Your ntfy server username",
                                placeholder: "Username",
                                text: $configManager.receiveUsername
                            )

                            SettingsSecureField(
                                title: "Password",
                                subtitle: "Your ntfy server password",
                                placeholder: "Password",
                                text: $configManager.receivePassword
                            )
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                }
                .animation(.easeInOut(duration: 0.3), value: configManager.useReceiveAuthentication)
            }
        }
    }
    
    // MARK: - Advanced Settings
    private var advancedSettings: some View {
        VStack(alignment: .leading, spacing: 24) {
            SettingsGroupBox(title: "Performance", icon: "speedometer") {
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Performance Settings")
                                .font(.headline)
                                .fontWeight(.medium)

                            Text("These settings affect performance and resource usage.")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()
                    }

                    // Placeholder for future advanced settings
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Coming Soon")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)

                        Text("Advanced performance settings will be available in future versions.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.secondary.opacity(0.1))
                    )
                }
            }

            SettingsGroupBox(title: "Debug & Maintenance", icon: "wrench.and.screwdriver") {
                VStack(alignment: .leading, spacing: 16) {
                    Button(action: {
                        openLogDirectory()
                    }) {
                        HStack {
                            Image(systemName: "folder")
                                .foregroundColor(.blue)

                            VStack(alignment: .leading, spacing: 2) {
                                Text("Open Log Directory")
                                    .fontWeight(.medium)

                                Text("View application logs in Finder")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(.plain)

                    Divider()

                    Button(action: {
                        exportConfiguration()
                    }) {
                        HStack {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.green)

                            VStack(alignment: .leading, spacing: 2) {
                                Text("Export Configuration")
                                    .fontWeight(.medium)

                                Text("Save current settings to file")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(.plain)

                    Divider()

                    Button(action: {
                        importConfiguration()
                    }) {
                        HStack {
                            Image(systemName: "square.and.arrow.down")
                                .foregroundColor(.blue)

                            VStack(alignment: .leading, spacing: 2) {
                                Text("Import Configuration")
                                    .fontWeight(.medium)

                                Text("Load settings from file")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }
    
    // MARK: - Modern Footer Section
    private var modernFooterSection: some View {
        HStack {
            Button("Reset to Defaults") {
                showingResetAlert = true
            }
            .buttonStyle(.borderless)
            .foregroundColor(.red)
            .help("Reset all settings to default values")

            Spacer()

            Button("Save Configuration") {
                configManager.saveConfiguration()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
            .help("Save current configuration")
        }
        .padding(20)
        .background(
            Color(NSColor.controlBackgroundColor)
                .overlay(
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color(NSColor.separatorColor)),
                    alignment: .top
                )
        )
        .alert("Reset Settings", isPresented: $showingResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                configManager.resetToDefaults()
            }
        } message: {
            Text("This will reset all settings to their default values. This action cannot be undone.")
        }
    }
    
    // MARK: - Private Methods
    private func testConfiguration() {
        // TODO: Implement configuration testing
        print("Testing configuration...")
    }

    private func openLogDirectory() {
        let fileManager = FileManager.default

        // Get the application support directory
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first else {
            print("Could not find Application Support directory")
            return
        }

        // Create the app-specific directory path
        let appDirectory = appSupportURL.appendingPathComponent("NtfyClip")
        let logsDirectory = appDirectory.appendingPathComponent("Logs")

        // Create the logs directory if it doesn't exist
        do {
            try fileManager.createDirectory(at: logsDirectory, withIntermediateDirectories: true, attributes: nil)

            // Open the directory in Finder
            NSWorkspace.shared.open(logsDirectory)

            print("✅ Opened log directory: \(logsDirectory.path)")
        } catch {
            print("❌ Failed to create or open log directory: \(error)")

            // Fallback: try to open the parent directory
            if fileManager.fileExists(atPath: appDirectory.path) {
                NSWorkspace.shared.open(appDirectory)
            } else {
                NSWorkspace.shared.open(appSupportURL)
            }
        }
    }

    private func exportConfiguration() {
        // Create configuration dictionary
        let configuration: [String: Any] = [
            "version": "1.0",
            "exportDate": ISO8601DateFormatter().string(from: Date()),
            "settings": [
                "enableSending": configManager.enableSending,
                "enableReceiving": configManager.enableReceiving,
                "sendServerURL": configManager.sendServerURL,
                "sendTopicName": configManager.sendTopicName,
                "receiveServerURL": configManager.receiveServerURL,
                "receiveTopicName": configManager.receiveTopicName,
                "useSendAuthentication": configManager.useSendAuthentication,
                "useReceiveAuthentication": configManager.useReceiveAuthentication,
                // Note: We don't export passwords for security reasons
                "sendUsername": configManager.useSendAuthentication ? configManager.sendUsername : "",
                "receiveUsername": configManager.useReceiveAuthentication ? configManager.receiveUsername : ""
            ]
        ]

        // Convert to JSON
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: configuration, options: .prettyPrinted)

            // Show save panel
            let savePanel = NSSavePanel()
            savePanel.title = "Export Configuration"
            savePanel.message = "Choose where to save the configuration file"
            savePanel.nameFieldStringValue = "NtfyClip-Config-\(DateFormatter.filenameDateFormatter.string(from: Date())).json"
            savePanel.allowedContentTypes = [.json]
            savePanel.canCreateDirectories = true

            savePanel.begin { response in
                if response == .OK, let url = savePanel.url {
                    do {
                        try jsonData.write(to: url)
                        print("✅ Configuration exported to: \(url.path)")

                        // Show success notification
                        DispatchQueue.main.async {
                            let notification = NSUserNotification()
                            notification.title = "Configuration Exported"
                            notification.informativeText = "Configuration saved to \(url.lastPathComponent)"
                            notification.soundName = NSUserNotificationDefaultSoundName
                            NSUserNotificationCenter.default.deliver(notification)
                        }
                    } catch {
                        print("❌ Failed to export configuration: \(error)")
                    }
                }
            }
        } catch {
            print("❌ Failed to serialize configuration: \(error)")
        }
    }

    private func importConfiguration() {
        let openPanel = NSOpenPanel()
        openPanel.title = "Import Configuration"
        openPanel.message = "Choose a configuration file to import"
        openPanel.allowedContentTypes = [.json]
        openPanel.allowsMultipleSelection = false
        openPanel.canChooseDirectories = false
        openPanel.canChooseFiles = true

        openPanel.begin { response in
            if response == .OK, let url = openPanel.url {
                do {
                    let jsonData = try Data(contentsOf: url)
                    let configuration = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any]

                    guard let config = configuration,
                          let settings = config["settings"] as? [String: Any] else {
                        print("❌ Invalid configuration file format")
                        return
                    }

                    // Import settings
                    DispatchQueue.main.async {
                        if let enableSending = settings["enableSending"] as? Bool {
                            configManager.enableSending = enableSending
                        }
                        if let enableReceiving = settings["enableReceiving"] as? Bool {
                            configManager.enableReceiving = enableReceiving
                        }
                        if let sendServerURL = settings["sendServerURL"] as? String {
                            configManager.sendServerURL = sendServerURL
                        }
                        if let sendTopicName = settings["sendTopicName"] as? String {
                            configManager.sendTopicName = sendTopicName
                        }
                        if let receiveServerURL = settings["receiveServerURL"] as? String {
                            configManager.receiveServerURL = receiveServerURL
                        }
                        if let receiveTopicName = settings["receiveTopicName"] as? String {
                            configManager.receiveTopicName = receiveTopicName
                        }
                        if let useSendAuth = settings["useSendAuthentication"] as? Bool {
                            configManager.useSendAuthentication = useSendAuth
                        }
                        if let useReceiveAuth = settings["useReceiveAuthentication"] as? Bool {
                            configManager.useReceiveAuthentication = useReceiveAuth
                        }
                        if let sendUsername = settings["sendUsername"] as? String {
                            configManager.sendUsername = sendUsername
                        }
                        if let receiveUsername = settings["receiveUsername"] as? String {
                            configManager.receiveUsername = receiveUsername
                        }

                        // Save the imported configuration
                        configManager.saveConfiguration()

                        print("✅ Configuration imported from: \(url.path)")

                        // Show success notification
                        let notification = NSUserNotification()
                        notification.title = "Configuration Imported"
                        notification.informativeText = "Settings loaded from \(url.lastPathComponent)"
                        notification.soundName = NSUserNotificationDefaultSoundName
                        NSUserNotificationCenter.default.deliver(notification)
                    }
                } catch {
                    print("❌ Failed to import configuration: \(error)")
                }
            }
        }
    }
}

// MARK: - Settings Tabs
enum SettingsTab: String, CaseIterable {
    case general = "General"
    case send = "Send"
    case receive = "Receive"
    case advanced = "Advanced"

    var title: String { rawValue }

    var icon: String {
        switch self {
        case .general:
            return "gearshape"
        case .send:
            return "arrow.up.circle"
        case .receive:
            return "arrow.down.circle"
        case .advanced:
            return "slider.horizontal.3"
        }
    }
}

// MARK: - Custom Settings Components

struct SettingsGroupBox<Content: View>: View {
    let title: String
    let icon: String
    let content: Content

    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.accentColor)
                    .font(.title3)

                Text(title)
                    .font(.title2)
                    .fontWeight(.semibold)

                Spacer()
            }

            content
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                )
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

struct SettingsToggle: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .toggleStyle(.switch)
        }
    }
}

struct SettingsTextField: View {
    let title: String
    let subtitle: String
    let placeholder: String
    @Binding var text: String

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            TextField(placeholder, text: $text)
                .textFieldStyle(.roundedBorder)
                .font(.body)
        }
    }
}

struct SettingsSecureField: View {
    let title: String
    let subtitle: String
    let placeholder: String
    @Binding var text: String

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            SecureField(placeholder, text: $text)
                .textFieldStyle(.roundedBorder)
                .font(.body)
        }
    }
}
