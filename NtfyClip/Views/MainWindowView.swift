import SwiftUI

struct MainWindowView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @StateObject private var logManager = LogManager.shared
    @State private var selectedSidebarItem: SidebarItem = .status

    var body: some View {
        NavigationSplitView {
            // Clean Sidebar without liquid glass
            SidebarView(selectedItem: $selectedSidebarItem)
                .navigationSplitViewColumnWidth(min: 280, ideal: 320, max: 380)
        } detail: {
            // Detail View with proper content width constraints
            HStack {
                Spacer(minLength: 0)

                DetailView(selectedItem: selectedSidebarItem)
                    .frame(maxWidth: 1200) // Limit maximum content width
                    .background(
                        LinearGradient(
                            colors: [
                                Color.blue.opacity(0.08),
                                Color.purple.opacity(0.04),
                                Color.clear
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                Spacer(minLength: 0)
            }
            .navigationSplitViewColumnWidth(min: 500, ideal: 700, max: .infinity)
        }
        .navigationTitle("NtfyClip")
        .toolbar {
            ToolbarItem(placement: .navigation) {
                Button(action: {
                    appViewModel.toggleSync()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: appViewModel.isSyncEnabled ? "stop.circle.fill" : "play.circle.fill")
                            .foregroundColor(appViewModel.isSyncEnabled ? .red : .green)
                            .font(.system(size: 14, weight: .medium))

                        Text(appViewModel.isSyncEnabled ? "Stop" : "Start")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.primary)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.primary.opacity(0.08))
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .help(appViewModel.isSyncEnabled ? "Stop Service" : "Start Service")
            }
        }
        .frame(minWidth: 1000, minHeight: 700)
        .background(
            LinearGradient(
                colors: [
                    Color.blue.opacity(0.05),
                    Color.purple.opacity(0.03),
                    Color.clear
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

// MARK: - Sidebar Items
enum SidebarItem: String, CaseIterable, Identifiable {
    case status = "Status"
    case settings = "Settings"
    case logs = "Logs"

    var id: String { rawValue }

    var icon: String {
        switch self {
        case .status:
            return "chart.line.uptrend.xyaxis"
        case .settings:
            return "gearshape"
        case .logs:
            return "doc.text"
        }
    }
}

// MARK: - Sidebar View
struct SidebarView: View {
    @Binding var selectedItem: SidebarItem
    @EnvironmentObject private var appViewModel: AppViewModel

    var body: some View {
        VStack(spacing: 0) {
            // Header with app info - Clean design without liquid glass
            VStack(spacing: 12) {
                // Logo with clean, simple design
                VStack(spacing: 8) {
                    // Simple logo without complex background
                    Text("📋")
                        .font(.system(size: 32))
                        .padding(8)
                        .background(
                            Circle()
                                .fill(Color.primary.opacity(0.05))
                        )

                    VStack(spacing: 4) {
                        Text("NtfyClip")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text("Clipboard Sync")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 20)
            .padding(.horizontal, 16)

            // Custom Navigation List - No List component to avoid selection styling
            VStack(spacing: 4) {
                ForEach(SidebarItem.allCases) { item in
                    Button(action: {
                        selectedItem = item
                    }) {
                        SidebarItemView(item: item, isSelected: selectedItem == item)
                    }
                    .buttonStyle(PlainButtonStyle()) // Remove default button styling
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)

            Spacer()

            // Footer with sync control - Clean design
            VStack(spacing: 16) {
                Divider()
                    .opacity(0.3)

                Button(action: {
                    appViewModel.toggleSync()
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: appViewModel.isSyncEnabled ? "stop.circle.fill" : "play.circle.fill")
                            .foregroundColor(appViewModel.isSyncEnabled ? .red : .green)
                            .font(.system(size: 16, weight: .medium))

                        Text(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.primary.opacity(0.08))
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .help(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 20)
        }
        .navigationTitle("NtfyClip")
    }
}

// MARK: - Sidebar Item View
struct SidebarItemView: View {
    let item: SidebarItem
    let isSelected: Bool
    @State private var isHovered = false

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: item.icon)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(isSelected ? .accentColor : (isHovered ? .accentColor : .primary))
                .frame(width: 24, height: 24)

            Text(item.rawValue)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(isSelected ? .primary : .primary)
                .fontWeight(isSelected ? .semibold : .medium)

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            Group {
                if isSelected {
                    // Clean selected state - simple and elegant
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.accentColor.opacity(0.15))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.accentColor.opacity(0.3), lineWidth: 1)
                        )
                } else if isHovered {
                    // Subtle hover state
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.primary.opacity(0.05))
                } else {
                    Color.clear
                }
            }
        )
        .contentShape(Rectangle())
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.15)) {
                isHovered = hovering
            }
        }
        .animation(.easeInOut(duration: 0.15), value: isSelected)
    }
}

// MARK: - Detail View
struct DetailView: View {
    let selectedItem: SidebarItem
    @EnvironmentObject private var appViewModel: AppViewModel

    var body: some View {
        UnifiedPageContainer {
            switch selectedItem {
            case .status:
                StatusDetailView()
            case .settings:
                SettingsDetailView()
            case .logs:
                LogsDetailView()
            }
        }
    }
}

// MARK: - Unified Page Container
struct UnifiedPageContainer<Content: View>: View {
    let content: Content

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        content
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(
                Color(NSColor.windowBackgroundColor)
                    .overlay(
                        // Subtle texture overlay for consistency
                        Rectangle()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color.primary.opacity(0.02),
                                        Color.clear,
                                        Color.primary.opacity(0.01)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
            )
    }
}


