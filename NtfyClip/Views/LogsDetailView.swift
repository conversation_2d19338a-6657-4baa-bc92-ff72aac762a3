import SwiftUI

struct LogsDetailView: View {
    @StateObject private var logManager = LogManager.shared
    @State private var searchText = ""
    @State private var selectedLevel: LogLevel? = nil
    @State private var showingExportSheet = false
    @State private var exportedLogs = ""
    @State private var availableWidth: CGFloat = 0
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Header with controls
                modernHeaderSection

                // Log list
                modernLogListSection
            }
            .onAppear {
                availableWidth = geometry.size.width
            }
            .onChange(of: geometry.size.width) { newWidth in
                availableWidth = newWidth
            }
        }
        .navigationTitle("Logs")
        .sheet(isPresented: $showingExportSheet) {
            LogExportView(logs: exportedLogs)
        }
    }

    // MARK: - Responsive Layout Properties
    private var isCompactLayout: Bool {
        availableWidth < 600
    }

    private var isMinimalLayout: Bool {
        availableWidth < 400
    }

    // MARK: - Responsive Action Buttons
    private var responsiveActionButtons: some View {
        Group {
            if isMinimalLayout {
                // Minimal layout: Vertical stack with icon-only buttons
                VStack(spacing: 8) {
                    HStack(spacing: 8) {
                        Button(action: {
                            exportedLogs = logManager.exportLogs()
                            showingExportSheet = true
                        }) {
                            Image(systemName: "square.and.arrow.up")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.regular)
                        .help("Export Logs")

                        Button(action: {
                            logManager.clearLogs()
                        }) {
                            Image(systemName: "trash")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.regular)
                        .foregroundColor(.red)
                        .help("Clear Logs")
                    }

                    if !searchText.isEmpty || selectedLevel != nil {
                        Button("Clear Filters") {
                            searchText = ""
                            selectedLevel = nil
                            logManager.setSearchText("")
                            logManager.setLevelFilter(nil)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                        .font(.caption)
                    }
                }
            } else if isCompactLayout {
                // Compact layout: Horizontal with shorter text
                HStack(spacing: 8) {
                    Button(action: {
                        exportedLogs = logManager.exportLogs()
                        showingExportSheet = true
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "square.and.arrow.up")
                                .font(.system(size: 14))
                            Text("Export")
                                .font(.caption)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.regular)

                    Button(action: {
                        logManager.clearLogs()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "trash")
                                .font(.system(size: 14))
                            Text("Clear")
                                .font(.caption)
                        }
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.regular)
                    .foregroundColor(.red)

                    if !searchText.isEmpty || selectedLevel != nil {
                        Button("Filters") {
                            searchText = ""
                            selectedLevel = nil
                            logManager.setSearchText("")
                            logManager.setLevelFilter(nil)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.regular)
                        .font(.caption)
                    }
                }
            } else {
                // Full layout: Original design
                HStack(spacing: 12) {
                    Button(action: {
                        exportedLogs = logManager.exportLogs()
                        showingExportSheet = true
                    }) {
                        HStack {
                            Image(systemName: "square.and.arrow.up")
                            Text("Export")
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)

                    Button(action: {
                        logManager.clearLogs()
                    }) {
                        HStack {
                            Image(systemName: "trash")
                            Text("Clear")
                        }
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.large)
                    .foregroundColor(.red)

                    if !searchText.isEmpty || selectedLevel != nil {
                        Button("Clear Filters") {
                            searchText = ""
                            selectedLevel = nil
                            logManager.setSearchText("")
                            logManager.setLevelFilter(nil)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.large)
                    }
                }
            }
        }
    }

    // MARK: - Responsive Search and Filters
    private var responsiveSearchAndFilters: some View {
        Group {
            if isMinimalLayout {
                // Minimal layout: Vertical stack with essential controls only
                VStack(spacing: 12) {
                    // Search field - full width
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                            .font(.system(size: 14))

                        TextField("Search...", text: $searchText)
                            .textFieldStyle(.plain)
                            .font(.body)
                            .onChange(of: searchText) { newValue in
                                logManager.setSearchText(newValue)
                            }

                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                                logManager.setSearchText("")
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                                    .font(.system(size: 14))
                            }
                            .buttonStyle(.plain)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.thickMaterial)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                            )
                    )

                    // Compact filter controls
                    HStack(spacing: 8) {
                        Picker("Level", selection: $logManager.minimumDisplayLevel) {
                            ForEach(LogLevel.allCases, id: \.self) { level in
                                Text(level.shortName).tag(level)
                            }
                        }
                        .pickerStyle(.menu)
                        .controlSize(.small)

                        Toggle("Block Debug", isOn: Binding(
                            get: { logManager.isDebugBlocked },
                            set: { logManager.setDebugBlocked($0) }
                        ))
                        .toggleStyle(.checkbox)
                        .controlSize(.small)
                        .font(.caption2)
                    }
                }
            } else if isCompactLayout {
                // Compact layout: Horizontal with reduced spacing
                VStack(spacing: 12) {
                    HStack(spacing: 12) {
                        // Search field
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.secondary)
                                .font(.system(size: 14))

                            TextField("Search logs...", text: $searchText)
                                .textFieldStyle(.plain)
                                .font(.body)
                                .onChange(of: searchText) { newValue in
                                    logManager.setSearchText(newValue)
                                }

                            if !searchText.isEmpty {
                                Button(action: {
                                    searchText = ""
                                    logManager.setSearchText("")
                                }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.secondary)
                                        .font(.system(size: 14))
                                }
                                .buttonStyle(.plain)
                            }
                        }
                        .padding(.horizontal, 10)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(.thickMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .frame(maxWidth: 250)

                        // Compact filters
                        HStack(spacing: 8) {
                            Picker("Min", selection: $logManager.minimumDisplayLevel) {
                                ForEach(LogLevel.allCases, id: \.self) { level in
                                    Text(level.shortName).tag(level)
                                }
                            }
                            .pickerStyle(.menu)
                            .controlSize(.small)

                            Toggle("Debug", isOn: Binding(
                                get: { logManager.isDebugBlocked },
                                set: { logManager.setDebugBlocked($0) }
                            ))
                            .toggleStyle(.checkbox)
                            .controlSize(.small)
                        }
                    }
                }
            } else {
                // Full layout: Original design
                HStack(spacing: 16) {
                    // Search field
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                            .font(.system(size: 14))

                        TextField("Search logs...", text: $searchText)
                            .textFieldStyle(.plain)
                            .font(.body)
                            .onChange(of: searchText) { newValue in
                                logManager.setSearchText(newValue)
                            }

                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                                logManager.setSearchText("")
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                                    .font(.system(size: 14))
                            }
                            .buttonStyle(.plain)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.thickMaterial)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                            )
                    )
                    .frame(maxWidth: 300)

                    // Minimum level filter
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Min Level")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Picker("Minimum Level", selection: $logManager.minimumDisplayLevel) {
                            ForEach(LogLevel.allCases, id: \.self) { level in
                                Label(level.displayName, systemImage: level.icon)
                                    .tag(level)
                            }
                        }
                        .pickerStyle(.menu)
                        .onChange(of: logManager.minimumDisplayLevel) { newValue in
                            logManager.setMinimumDisplayLevel(newValue)
                        }
                    }

                    // Specific level filter
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Filter")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Picker("Level", selection: $selectedLevel) {
                            Text("All Levels").tag(LogLevel?.none)
                            ForEach(LogLevel.allCases, id: \.self) { level in
                                Label(level.displayName, systemImage: level.icon)
                                    .tag(LogLevel?.some(level))
                            }
                        }
                        .pickerStyle(.menu)
                        .onChange(of: selectedLevel) { newValue in
                            logManager.setLevelFilter(newValue)
                        }
                    }

                    // Debug blocking toggle
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Debug Control")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Toggle(isOn: Binding(
                            get: { logManager.isDebugBlocked },
                            set: { logManager.setDebugBlocked($0) }
                        )) {
                            HStack(spacing: 6) {
                                Image(systemName: "ladybug")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                                Text("Block Debug")
                                    .font(.caption2)
                            }
                        }
                        .toggleStyle(.checkbox)
                        .help("When enabled, debug level logs are not recorded in the UI")
                    }

                    Spacer()
                }
            }
        }
    }

    // MARK: - Responsive Statistics and Actions
    private var responsiveStatisticsAndActions: some View {
        Group {
            if isMinimalLayout {
                // Minimal layout: Vertical stack with essential stats only
                VStack(spacing: 12) {
                    // Essential stats only
                    HStack(spacing: 12) {
                        ModernStatBadge(
                            title: "Show",
                            count: logManager.filteredLogs.count,
                            color: .blue,
                            icon: "doc.text"
                        )

                        ModernStatBadge(
                            title: "Total",
                            count: logManager.logs.count,
                            color: .secondary,
                            icon: "doc.text.fill"
                        )

                        if logManager.errorCount > 0 {
                            ModernStatBadge(
                                title: "Err",
                                count: logManager.errorCount,
                                color: .red,
                                icon: "xmark.circle"
                            )
                        }
                    }

                    // Actions
                    responsiveActionButtons
                }
            } else if isCompactLayout {
                // Compact layout: Horizontal with reduced spacing
                VStack(spacing: 12) {
                    HStack {
                        // Statistics
                        HStack(spacing: 12) {
                            ModernStatBadge(
                                title: "Showing",
                                count: logManager.filteredLogs.count,
                                color: .blue,
                                icon: "doc.text"
                            )

                            ModernStatBadge(
                                title: "Total",
                                count: logManager.logs.count,
                                color: .secondary,
                                icon: "doc.text.fill"
                            )

                            ModernStatBadge(
                                title: "Errors",
                                count: logManager.errorCount,
                                color: .red,
                                icon: "xmark.circle"
                            )
                        }

                        Spacer()

                        // Quick buttons - compact
                        HStack(spacing: 6) {
                            Button("I+") {
                                logManager.setMinimumDisplayLevel(.info)
                            }
                            .buttonStyle(.bordered)
                            .controlSize(.mini)
                            .disabled(logManager.minimumDisplayLevel == .info)
                            .help("Info and above")

                            Button("W+") {
                                logManager.setMinimumDisplayLevel(.warning)
                            }
                            .buttonStyle(.bordered)
                            .controlSize(.mini)
                            .disabled(logManager.minimumDisplayLevel == .warning)
                            .help("Warning and above")

                            Button("All") {
                                logManager.setMinimumDisplayLevel(.debug)
                            }
                            .buttonStyle(.bordered)
                            .controlSize(.mini)
                            .disabled(logManager.minimumDisplayLevel == .debug)
                        }
                    }

                    HStack {
                        Spacer()
                        responsiveActionButtons
                    }
                }
            } else {
                // Full layout: Original design
                HStack {
                    // Statistics
                    HStack(spacing: 20) {
                        ModernStatBadge(
                            title: "Showing",
                            count: logManager.filteredLogs.count,
                            color: .blue,
                            icon: "doc.text"
                        )

                        ModernStatBadge(
                            title: "Total",
                            count: logManager.logs.count,
                            color: .secondary,
                            icon: "doc.text.fill"
                        )

                        ModernStatBadge(
                            title: "Errors",
                            count: logManager.errorCount,
                            color: .red,
                            icon: "xmark.circle"
                        )

                        ModernStatBadge(
                            title: "Warnings",
                            count: logManager.warningCount,
                            color: .orange,
                            icon: "exclamationmark.triangle"
                        )
                    }

                    // Quick level buttons
                    HStack(spacing: 8) {
                        Text("Quick:")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Button("Info+") {
                            logManager.setMinimumDisplayLevel(.info)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                        .disabled(logManager.minimumDisplayLevel == .info)

                        Button("Warn+") {
                            logManager.setMinimumDisplayLevel(.warning)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                        .disabled(logManager.minimumDisplayLevel == .warning)

                        Button("All") {
                            logManager.setMinimumDisplayLevel(.debug)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                        .disabled(logManager.minimumDisplayLevel == .debug)
                    }

                    Spacer()

                    // Actions
                    responsiveActionButtons
                }
            }
        }
    }

    // MARK: - Responsive Title
    private var responsiveTitle: some View {
        Group {
            if isMinimalLayout {
                // Minimal layout: Compact title
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 12) {
                        Image(systemName: "doc.text")
                            .font(.system(size: 24, weight: .medium))
                            .foregroundStyle(.blue.gradient)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(.blue.opacity(0.1))
                            )

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Logs")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text("App activity")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()
                    }
                }
            } else if isCompactLayout {
                // Compact layout: Reduced spacing
                HStack {
                    HStack(spacing: 12) {
                        Image(systemName: "doc.text")
                            .font(.system(size: 28, weight: .medium))
                            .foregroundStyle(.blue.gradient)
                            .frame(width: 36, height: 36)
                            .background(
                                Circle()
                                    .fill(.blue.opacity(0.1))
                            )

                        VStack(alignment: .leading, spacing: 3) {
                            Text("Application Logs")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text("View and export logs")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()
                }
            } else {
                // Full layout: Original design
                HStack {
                    HStack(spacing: 16) {
                        Image(systemName: "doc.text")
                            .font(.system(size: 32, weight: .medium))
                            .foregroundStyle(.blue.gradient)
                            .frame(width: 40, height: 40)
                            .background(
                                Circle()
                                    .fill(.blue.opacity(0.1))
                            )

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Application Logs")
                                .font(.title)
                                .fontWeight(.bold)

                            Text("View and export application activity logs")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()
                }
            }
        }
    }
    
    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(spacing: 24) {
            // Title and description - Responsive
            responsiveTitle

            // Search and filter controls - Responsive Layout
            responsiveSearchAndFilters


            // Statistics and actions - Responsive Layout
            responsiveStatisticsAndActions
        }
        .padding(.horizontal, 32)
        .padding(.vertical, 24)
        .background(Color(NSColor.controlBackgroundColor))
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color(NSColor.separatorColor)),
            alignment: .bottom
        )
    }
    
    // MARK: - Modern Log List Section
    private var modernLogListSection: some View {
        Group {
            if logManager.filteredLogs.isEmpty {
                // Empty state
                VStack(spacing: 20) {
                    Image(systemName: "doc.text.magnifyingglass")
                        .font(.system(size: 64, weight: .light))
                        .foregroundColor(.secondary)

                    VStack(spacing: 8) {
                        Text("No logs to display")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        Text(searchText.isEmpty && selectedLevel == nil ? "Logs will appear here as the application runs" : "No logs match your search criteria")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }

                    if !searchText.isEmpty || selectedLevel != nil {
                        Button("Clear Filters") {
                            searchText = ""
                            selectedLevel = nil
                            logManager.setSearchText("")
                            logManager.setLevelFilter(nil)
                        }
                        .buttonStyle(.bordered)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(NSColor.textBackgroundColor))
            } else {
                // Log entries
                List(logManager.filteredLogs.reversed(), id: \.id) { log in
                    ModernLogEntryRow(log: log)
                        .listRowInsets(EdgeInsets(top: 8, leading: 20, bottom: 8, trailing: 20))
                        .listRowSeparator(.hidden)
                }
                .listStyle(.plain)
                .background(Color(NSColor.textBackgroundColor))
            }
        }
    }
}

// MARK: - Log Entry Row
struct LogEntryRow: View {
    let log: LogEntry
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            // Main row
            HStack(alignment: .top, spacing: 8) {
                // Level indicator
                Image(systemName: log.level.icon)
                    .foregroundColor(log.level.color)
                    .frame(width: 16)
                
                // Content
                VStack(alignment: .leading, spacing: 2) {
                    // Message
                    Text(log.message)
                        .font(.system(.body, design: .monospaced))
                        .lineLimit(isExpanded ? nil : 3)
                        .fixedSize(horizontal: false, vertical: true)
                    
                    // Metadata
                    HStack {
                        Text(log.timestamp, style: .time)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(log.category)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(log.level.rawValue)
                            .font(.caption)
                            .foregroundColor(log.level.color)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        if log.message.count > 100 {
                            Button(isExpanded ? "Less" : "More") {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    isExpanded.toggle()
                                }
                            }
                            .font(.caption)
                            .buttonStyle(.borderless)
                        }
                    }
                }
                
                Spacer()
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            if log.message.count > 100 {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
            }
        }
    }
}

// MARK: - Stat Badge
struct StatBadge: View {
    let title: String
    let count: Int
    let color: Color
    
    var body: some View {
        HStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(count)")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(color)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(color.opacity(0.1))
                .cornerRadius(4)
        }
    }
}

// MARK: - Log Export View
struct LogExportView: View {
    let logs: String
    @Environment(\.dismiss) private var dismiss
    @State private var showingSavePanel = false
    @State private var exportStatus: ExportStatus = .none

    enum ExportStatus: Equatable {
        case none
        case copying
        case saving
        case success(String)
        case error(String)

        static func == (lhs: ExportStatus, rhs: ExportStatus) -> Bool {
            switch (lhs, rhs) {
            case (.none, .none), (.copying, .copying), (.saving, .saving):
                return true
            case (.success(let lhsMessage), .success(let rhsMessage)):
                return lhsMessage == rhsMessage
            case (.error(let lhsMessage), .error(let rhsMessage)):
                return lhsMessage == rhsMessage
            default:
                return false
            }
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with export info
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "doc.text")
                            .font(.title2)
                            .foregroundColor(.blue)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Log Export")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Text("\(logs.components(separatedBy: .newlines).count - 3) log entries") // -3 for header lines
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        // Status indicator
                        if case .success(let message) = exportStatus {
                            HStack {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                Text(message)
                                    .font(.caption)
                                    .foregroundColor(.green)
                            }
                        } else if case .error(let message) = exportStatus {
                            HStack {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.red)
                                Text(message)
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    }

                    Divider()
                }
                .padding()
                .background(Color(NSColor.controlBackgroundColor))

                // Log content
                ScrollView {
                    Text(logs)
                        .font(.system(.caption, design: .monospaced))
                        .textSelection(.enabled)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                }
                .background(Color(NSColor.textBackgroundColor))

                // Action buttons
                VStack(spacing: 12) {
                    HStack(spacing: 12) {
                        Button(action: copyToClipboard) {
                            HStack {
                                if case .copying = exportStatus {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "doc.on.clipboard")
                                }
                                Text("Copy to Clipboard")
                            }
                        }
                        .buttonStyle(.bordered)
                        .disabled(exportStatus == .copying)

                        Button(action: saveToFile) {
                            HStack {
                                if case .saving = exportStatus {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "square.and.arrow.down")
                                }
                                Text("Save to File")
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(exportStatus == .saving)

                        Spacer()

                        Button("Done") {
                            dismiss()
                        }
                        .buttonStyle(.bordered)
                    }
                }
                .padding()
                .background(Color(NSColor.controlBackgroundColor))
                .overlay(
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color(NSColor.separatorColor)),
                    alignment: .top
                )
            }
            .navigationTitle("Export Logs")
            .frame(width: 700, height: 600)
        }
    }

    // MARK: - Private Methods
    private func copyToClipboard() {
        exportStatus = .copying

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            NSPasteboard.general.clearContents()
            NSPasteboard.general.setString(logs, forType: .string)

            exportStatus = .success("Copied to clipboard")

            // Clear status after 3 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                if case .success = exportStatus {
                    exportStatus = .none
                }
            }
        }
    }

    private func saveToFile() {
        exportStatus = .saving

        let savePanel = NSSavePanel()
        savePanel.title = "Save Logs"
        savePanel.message = "Choose where to save the log file"
        savePanel.nameFieldStringValue = "NtfyClip-Logs-\(DateFormatter.filenameDateFormatter.string(from: Date())).txt"
        savePanel.allowedContentTypes = [.plainText]
        savePanel.canCreateDirectories = true

        savePanel.begin { response in
            DispatchQueue.main.async {
                if response == .OK, let url = savePanel.url {
                    do {
                        try logs.write(to: url, atomically: true, encoding: .utf8)
                        exportStatus = .success("Saved to \(url.lastPathComponent)")

                        // Clear status after 3 seconds
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            if case .success = exportStatus {
                                exportStatus = .none
                            }
                        }
                    } catch {
                        exportStatus = .error("Failed to save: \(error.localizedDescription)")

                        // Clear error after 5 seconds
                        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                            if case .error = exportStatus {
                                exportStatus = .none
                            }
                        }
                    }
                } else {
                    exportStatus = .none
                }
            }
        }
    }
}

// MARK: - DateFormatter Extension
extension DateFormatter {
    static let filenameDateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd-HHmmss"
        return formatter
    }()
}

// MARK: - Modern Stat Badge
struct ModernStatBadge: View {
    let title: String
    let count: Int
    let color: Color
    let icon: String

    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(color.opacity(0.15))
                )

            VStack(alignment: .leading, spacing: 2) {
                Text("\(count)")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(.regularMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - Modern Log Entry Row
struct ModernLogEntryRow: View {
    let log: LogEntry
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(spacing: 12) {
                // Level indicator with background
                HStack {
                    Image(systemName: log.level.icon)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                }
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(log.level.color)
                )

                // Main content
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        // Category badge
                        Text(log.category)
                            .font(.caption2)
                            .fontWeight(.medium)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 3)
                            .background(
                                Capsule()
                                    .fill(log.level.color.opacity(0.15))
                            )
                            .foregroundColor(log.level.color)

                        Spacer()

                        // Timestamp
                        Text(log.timestamp, style: .time)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }

                    // Message
                    Text(log.message)
                        .font(.body)
                        .lineLimit(isExpanded ? nil : 3)
                        .multilineTextAlignment(.leading)
                        .animation(.easeInOut(duration: 0.2), value: isExpanded)
                }

                // Expand/collapse button
                if log.message.count > 100 {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isExpanded.toggle()
                        }
                    }) {
                        Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.thickMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                    )
                    .shadow(color: .black.opacity(0.08), radius: 2, x: 0, y: 1)
            )
        }
    }
}
