# NtfyClip 打包说明

## 构建环境要求

- macOS 13.0 或更高版本
- Xcode 15.0 或更高版本
- 支持 Apple Silicon (arm64) 架构

## 快速打包

### 方法一：使用自动化脚本（推荐）

1. 在项目根目录执行：
```bash
chmod +x build_and_package.sh
./build_and_package.sh
```

2. 脚本会自动：
   - 清理之前的构建
   - 构建 Release 版本
   - 创建 DMG 安装包
   - 输出打包结果

### 方法二：手动构建

1. **清理项目**
```bash
rm -rf DerivedData
```

2. **构建应用**
```bash
xcodebuild -project NtfyClip.xcodeproj -scheme NtfyClip -configuration Release -arch arm64 build
```

3. **查找构建结果**
```bash
find ~/Library/Developer/Xcode/DerivedData -name "NtfyClip.app" -type d
```

4. **创建 DMG**
```bash
# 创建临时目录
mkdir -p temp_dmg/NtfyClip
cp -R [应用路径]/NtfyClip.app temp_dmg/NtfyClip/
ln -s /Applications temp_dmg/NtfyClip/Applications

# 创建 DMG
hdiutil create -volname "NtfyClip" -srcfolder temp_dmg/NtfyClip -ov -format UDZO NtfyClip.dmg

# 清理
rm -rf temp_dmg
```

## 分发说明

### DMG 内容
- `NtfyClip.app` - 主应用程序
- `Applications` - 应用程序文件夹的符号链接

### 安装步骤
1. 双击 `NtfyClip-YYYYMMDD.dmg` 打开磁盘映像
2. 将 `NtfyClip.app` 拖拽到 `Applications` 文件夹
3. 从启动台或应用程序文件夹启动 NtfyClip

### 首次运行
由于应用未经过 Apple 公证，首次运行时：
1. 右键点击应用，选择"打开"
2. 在弹出的对话框中点击"打开"
3. 或在系统偏好设置 > 安全性与隐私中允许运行

## 故障排除

### 构建失败
1. 确保 Xcode 版本兼容
2. 清理 DerivedData：`rm -rf ~/Library/Developer/Xcode/DerivedData`
3. 重启 Xcode

### 应用无法启动
1. 检查 macOS 版本兼容性（需要 13.0+）
2. 确保已安装在 Applications 文件夹
3. 检查系统安全设置

### 权限问题
应用需要以下权限：
- 辅助功能权限（用于剪贴板监控）
- 网络权限（用于 ntfy 服务通信）

在系统偏好设置 > 安全性与隐私 > 隐私中授予相应权限。

## 开发者注意事项

### 代码签名
生产环境建议添加代码签名：
```bash
codesign --force --deep --sign "Developer ID Application: Your Name" NtfyClip.app
```

### 公证
对于分发，建议进行 Apple 公证：
```bash
xcrun notarytool submit NtfyClip.dmg --keychain-profile "notarytool-profile" --wait
```

### 版本管理
- 在 `Info.plist` 中更新版本号
- 使用语义化版本控制（如 1.0.0）
- 在 Git 中创建对应的标签

## 更新日志

### v1.0.0 (2025-07-08)
- ✅ 初始版本发布
- ✅ 支持剪贴板同步功能
- ✅ 统一的界面设计风格
- ✅ Debug 日志控制功能（默认屏蔽）
- ✅ 工具栏服务状态控制按钮
- ✅ 修复全屏布局问题
- ✅ 消除侧边栏黑色选中框
- ✅ 优化 Logo 显示效果
- ✅ 统一页面间距和设计
- ✅ 成功构建和打包为 DMG

### 已知问题
- 使用了已弃用的 NSUserNotification API（计划在未来版本中更新为 UserNotifications 框架）
- 部分代码中存在无法到达的 catch 块（不影响功能）

### 技术规格
- 支持 macOS 13.0 或更高版本
- 支持 Apple Silicon (arm64) 架构
- 应用大小：约 790KB
- 包含完整的调试符号文件 (.dSYM)
