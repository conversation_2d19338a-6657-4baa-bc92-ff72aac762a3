#!/bin/bash

# NtfyClip 构建和打包脚本
# 用于构建应用并创建 DMG 安装包

set -e

# 配置
APP_NAME="NtfyClip"
SCHEME_NAME="NtfyClip"
PROJECT_NAME="NtfyClip.xcodeproj"
BUILD_CONFIG="Release"
DERIVED_DATA_PATH="./DerivedData"
DMG_NAME="${APP_NAME}-$(date +%Y%m%d)"

echo "🚀 开始构建 ${APP_NAME}..."

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf "${DERIVED_DATA_PATH}"
rm -f "${DMG_NAME}.dmg"

# 构建应用
echo "🔨 构建应用..."
xcodebuild \
    -project "${PROJECT_NAME}" \
    -scheme "${SCHEME_NAME}" \
    -configuration "${BUILD_CONFIG}" \
    -derivedDataPath "${DERIVED_DATA_PATH}" \
    -arch arm64 \
    build

# 查找构建的应用
APP_PATH="${DERIVED_DATA_PATH}/Build/Products/${BUILD_CONFIG}/${APP_NAME}.app"

if [ ! -d "${APP_PATH}" ]; then
    echo "❌ 构建失败：找不到应用文件"
    exit 1
fi

echo "✅ 构建成功：${APP_PATH}"

# 创建临时目录用于 DMG
TEMP_DIR=$(mktemp -d)
DMG_DIR="${TEMP_DIR}/${APP_NAME}"
mkdir -p "${DMG_DIR}"

# 复制应用到临时目录
echo "📦 准备 DMG 内容..."
cp -R "${APP_PATH}" "${DMG_DIR}/"

# 创建应用程序文件夹的符号链接
ln -s /Applications "${DMG_DIR}/Applications"

# 创建 DMG
echo "💿 创建 DMG..."
hdiutil create \
    -volname "${APP_NAME}" \
    -srcfolder "${DMG_DIR}" \
    -ov \
    -format UDZO \
    "${DMG_NAME}.dmg"

# 清理临时文件
rm -rf "${TEMP_DIR}"

echo "🎉 打包完成！"
echo "📁 DMG 文件：${DMG_NAME}.dmg"
echo ""
echo "安装说明："
echo "1. 双击 ${DMG_NAME}.dmg 打开磁盘映像"
echo "2. 将 ${APP_NAME}.app 拖拽到 Applications 文件夹"
echo "3. 从 Applications 文件夹启动 ${APP_NAME}"
